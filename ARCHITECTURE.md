# OSI 模型交互式可视化项目架构设计文档

## 1. 概述

本文档旨在为 "OSI 模型交互式可视化" 前端项目提供全面的架构设计方案。项目的目标是创建一个顶级的、具有专业水准和科技美学的交互式网站，用于直观地展示 OSI 模型的原理和工作流程。核心视觉风格将采用具有沉浸感的 **3D 场景**，并融入赛博朋克或科幻美学元素。

---

## 2. 技术栈选型

为了实现高性能的 3D 交互和流畅的动画效果，同时保证代码的可维护性和可扩展性，我们选择以下技术栈：

### 2.1. 前端框架: React (Next.js)

*   **选择理由:**
    *   **声明式 UI 与组件化:** React 的核心思想与构建复杂 3D 场景的需求高度契合。我们可以将场景中的每一个元素（如层、数据包）都抽象为独立的、可复用的组件。
    *   **强大的生态系统:** React 拥有庞大的社区和丰富的第三方库，特别是 `React Three Fiber`，它是我们实现 3D 场景的关键。
    *   **React Three Fiber (R3F):** 这是一个用于 Three.js 的 React 渲染器。它允许我们以声明式、组件化的方式构建和管理复杂的 Three.js 场景，极大地降低了直接操作 Three.js 的复杂性，使代码更易于维护。
    *   **Next.js 框架优势:** 虽然项目核心是客户端的重度交互，但 Next.js 提供了强大的项目脚手架、优化的构建流程、文件系统路由以及未来进行 SEO 或添加静态信息页面的能力。

### 2.2. 3D/可视化库: Three.js (通过 React Three Fiber)

*   **选择理由:**
    *   **满足 3D 需求:** Three.js 是 WebGL 的事实标准，能够创建用户所期望的、具有深度和沉浸感的 3D 场景，完美契合项目的视觉目标。
    *   **高性能:** 它利用 GPU 进行硬件加速渲染，是实现复杂、流畅动画效果的性能保障。
    *   **生态集成:** 通过 R3F，它可以无缝地融入 React 生态。我们将利用 R3F 的生态库，如 `Drei`（提供相机、控制器、辅助工具等常用抽象）和 `Framer Motion 3D` 或 `react-spring`（用于实现物理和状态驱动的动画），来加速开发。

### 2.3. 样式方案: Tailwind CSS

*   **选择理由:**
    *   **开发效率:** 原子化的 CSS 类库可以让我们在 JSX/TSX 中快速构建用户界面（如控制面板、信息窗），而无需在多个文件之间切换。
    *   **设计一致性:** 通过 `tailwind.config.ts` 文件，我们可以定义一套符合项目赛博朋克/科幻主题的设计规范（颜色、间距、字体等），确保整体视觉风格的统一。
    *   **性能优化:** Tailwind CSS 在生产构建时会自动移除所有未使用的样式，生成极小的 CSS 文件，不会对页面加载性能造成负担。
    *   **关注点分离:** 它能很好地处理 2D UI 的样式，而不会与 Three.js 的 3D 场景渲染产生冲突。

---

## 3. 项目结构设计

我们将采用一种功能驱动（Feature-Driven）和模块化的目录结构，以确保代码的清晰度和可扩展性。

```
/
├── public/                  # 静态资源 (3D模型, 字体, 图像)
│   ├── models/              # .glb, .gltf 等3D模型文件
│   └── textures/            # 纹理图片
├── src/
│   ├── app/                 # Next.js App Router: 页面和路由
│   │   └── page.tsx         # 应用主页面，将承载3D场景
│   ├── components/          # 全局通用、可复用的2D UI组件
│   │   ├── ui/              # 基础UI原子组件 (如 Button, Slider, Panel)
│   │   └── layout/          # 页面布局组件 (如 Header, Footer)
│   ├── features/            # 核心功能模块
│   │   └── osi-visualizer/
│   │       ├── components/  # 特定于OSI可视化的3D和2D组件
│   │       │   ├── Scene.tsx          # 主3D场景容器
│   │       │   ├── OsiLayer3D.tsx     # 3D OSI层级组件
│   │       │   ├── DataPacket3D.tsx   # 3D 数据包组件
│   │       │   ├── ControlPanel.tsx   # 2D 交互控制面板
│   │       │   └── InfoDisplay.tsx    # 2D 信息展示面板
│   │       ├── hooks/       # 特定于此功能的React Hooks
│   │       ├── store/       # 状态管理 (Zustand)
│   │       │   ├── index.ts           # Zustand store 的定义和初始化
│   │       │   └── types.ts           # store 相关的类型定义
│   │       └── data/        # 静态数据
│   │           └── osi-layers-data.ts # OSI七层模型的详细描述信息
│   ├── lib/                 # 全局工具函数、辅助模块
│   ├── styles/              # 全局样式
│   │   └── globals.css      # Tailwind CSS 基础指令和全局样式
│   └── types/               # 全局TypeScript类型定义
│       └── common.d.ts
├── .eslintrc.json           # ESLint 配置文件
├── next.config.mjs          # Next.js 配置文件
├── postcss.config.js        # PostCSS 配置文件 (供Tailwind使用)
├── tailwind.config.ts       # Tailwind CSS 配置文件
└── tsconfig.json            # TypeScript 配置文件
```

---

## 4. 核心组件规划

以下是构成应用核心的组件及其职责：

*   **`Scene.tsx` (3D)**
    *   **职责:** 作为主画布，负责设置 Three.js 场景、相机、光照和环境。它将组合所有的 3D 组件，并作为 React 和 Three.js 世界的桥梁。

*   **`OsiLayer3D.tsx` (3D)**
    *   **职责:** 代表 OSI 模型中的单个层级。它是一个 3D 对象，可以根据当前状态（如 `active`, `highlighted`）改变其外观（如颜色、发光效果）。
    *   **Props:** `layerId`, `name`, `position` 等。

*   **`DataPacket3D.tsx` (3D)**
    *   **职责:** 代表在模型中流动的数据包。它的位置、大小和视觉表现将完全由全局状态驱动。当数据包被封装或解封装时，它会通过动画（如增加/移除一个外壳）来直观地展示这一过程。
    *   **Props:** `position`, `state`, `encapsulationLevel` 等。

*   **`ControlPanel.tsx` (2D)**
    *   **职责:** 浮于 3D 场景之上的 HTML UI。提供播放/暂停/重置动画、选择不同网络场景（如 HTTP 请求、FTP 文件传输）等交互控件。用户的操作将调用状态管理库中的 `actions` 来更新全局状态。

*   **`InfoDisplay.tsx` (2D)**
    *   **职责:** 同样是浮于 3D 场景之上的 HTML UI。它会订阅全局状态，当用户与某个 `OsiLayer3D` 交互或动画进行到特定步骤时，动态显示该层的详细信息、协议示例以及数据包的当前状态。

---

## 5. 数据与状态管理

### 5.1. 状态管理库: Zustand

*   **选择理由:**
    *   **简洁高效:** Zustand 提供了一个非常小巧和简单的 API，没有 Redux 那样的模板代码，能快速上手。
    *   **性能优化:** 组件可以精确订阅它们所依赖的状态片段。这意味着只有当被订阅的状态发生变化时，组件才会重新渲染。这对于避免在复杂的 3D 场景中发生不必要的渲染至关重要。
    *   **逻辑解耦:** 我们可以将所有复杂的业务逻辑（如动画序列的计算、数据包状态的转换）都封装在 store 的 `actions` 中，让 React 组件保持纯粹，只负责渲染和触发动作。

### 5.2. 全局状态结构 (State Shape)

下面是一个简化的全局状态树示例：

```typescript
// src/features/osi-visualizer/store/types.ts

// 定义动画的各个阶段
type AnimationStatus = 'idle' | 'playing' | 'paused' | 'finished';

// 数据包在每一层的状态
interface PacketState {
  layer: number; // 当前所在层级的索引 (0-6)
  direction: 'down' | 'up'; // 数据流向：封装或解封装
  description: string; // 对当前变化的描述
}

// Zustand Store 的完整接口
export interface OsiVisualizerState {
  // 静态数据
  layersData: Array<{ name: string; description: string; }>;

  // 动态状态
  packetState: PacketState;
  animationStatus: AnimationStatus;
  activeLayerIndex: number | null; // 当前高亮的层级

  // Actions: 用于更新状态的函数
  actions: {
    play: () => void;
    pause: () => void;
    reset: () => void;
    goToStep: (step: number) => void;
    setActiveLayer: (index: number | null) => void;
  };
}
```

### 5.3. 数据流

1.  **初始化:** 应用加载时，从 `osi-layers-data.ts` 读取七层模型的静态信息，并初始化到 Zustand store 中。
2.  **用户交互:** 用户点击 `ControlPanel.tsx` 上的 "播放" 按钮。
3.  **动作调用:** `ControlPanel.tsx` 调用 store 中的 `actions.play()`。
4.  **状态更新:** `play` action 内部会使用 `setInterval` 或 `requestAnimationFrame` 来逐步更新 `packetState` 和 `animationStatus`。
5.  **组件响应:**
    *   `DataPacket3D.tsx` 订阅 `packetState`，并根据新的位置和状态通过动画更新自己的 3D transform。
    *   `OsiLayer3D.tsx` 订阅 `activeLayerIndex`，并在数据包到达时高亮自己。
    *   `InfoDisplay.tsx` 订阅 `packetState` 和 `activeLayerIndex`，显示当前步骤的详细信息。
6.  **循环:** 这个流程持续进行，直到动画播放完成。