"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import osiModelData from '@/data/osi-model-data.json';
import { OsiVisualization } from '@/components/3d/OsiVisualization';

// 定义数据类型，增强代码健壮性
type OsiLayer = {
  layer: number;
  name: string;
  englishName: string;
  pdu: string;
  description: string;
  details: string[];
};

export default function Home() {
  const [selectedLayer, setSelectedLayer] = useState<OsiLayer | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);

  const handleStartAnimation = () => {
    setIsAnimating(true);
    setSelectedLayer(null); // 清除选择，以显示3D视图
    // 动画结束后，需要将 isAnimating 设置回 false
    // 这个逻辑将在 OsiVisualization 组件中处理，并通过回调函数通知父组件
  };

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-8 md:p-24 bg-gray-900 text-white">
      <div className="w-full max-w-6xl">
        <h1 className="text-3xl md:text-5xl font-bold text-center mb-4 text-cyan-300">OSI 模型交互式可视化</h1>
        <p className="text-center text-gray-400 mb-8">一窥数据在网络中的奇妙旅程</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="col-span-1">
            <div className="flex flex-col space-y-2">
              {osiModelData.map((layer) => (
                <div
                  key={layer.layer}
                  className={`p-4 rounded-lg cursor-pointer transition-all duration-300 ${selectedLayer?.layer === layer.layer ? 'bg-cyan-500 shadow-lg scale-105' : 'bg-gray-800 hover:bg-gray-700'}`}
                  onClick={() => {
                    setSelectedLayer(layer as OsiLayer);
                    setIsAnimating(false); // 选择层级时停止动画
                  }}
                >
                  <p className="font-bold">{layer.layer}. {layer.name}</p>
                  <p className="text-sm text-gray-400">{layer.englishName}</p>
                </div>
              ))}
            </div>
            <button
              onClick={handleStartAnimation}
              disabled={isAnimating}
              className="w-full mt-4 p-3 bg-green-500 rounded-lg font-bold hover:bg-green-400 transition-colors disabled:bg-gray-600"
            >
              {isAnimating ? '动画播放中...' : '开始数据包传输动画'}
            </button>
          </div>
          <div className="col-span-2 rounded-lg min-h-[400px] md:min-h-[500px] flex flex-col">
            <AnimatePresence>
              {selectedLayer ? (
                <motion.div
                  key={selectedLayer.layer}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="bg-gray-800 p-6 rounded-lg"
                >
                  <h2 className="text-2xl font-bold mb-2 text-cyan-400">{selectedLayer.layer}. {selectedLayer.name} ({selectedLayer.englishName})</h2>
                  <p className="font-semibold text-lg mb-4">PDU: {selectedLayer.pdu}</p>
                  <p className="mb-4 text-gray-300">{selectedLayer.description}</p>
                  <h3 className="font-semibold text-xl mb-2 text-cyan-400">关键协议/技术:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-300">
                    {selectedLayer.details.map((detail, index) => (
                      <li key={index}>{detail}</li>
                    ))}
                  </ul>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 1 }}
                  className="flex-grow h-full"
                >
                  <OsiVisualization
                    isAnimating={isAnimating}
                    onAnimationComplete={() => setIsAnimating(false)}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </main>
  );
}
