"use client";

import { useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Box, Text, Stars } from '@react-three/drei';
import * as THREE from 'three';
import { Vector3 } from 'three';

interface OsiVisualizationProps {
  isAnimating: boolean;
  onAnimationComplete: () => void;
}

const osiLayersData = [
  { y: 3, name: "7. Application" },
  { y: 2, name: "6. Presentation" },
  { y: 1, name: "5. Session" },
  { y: 0, name: "4. Transport" },
  { y: -1, name: "3. Network" },
  { y: -2, name: "2. Data Link" },
  { y: -3, name: "1. Physical" },
];

const Packet = ({ isAnimating, onAnimationComplete }: OsiVisualizationProps) => {
  const groupRef = useRef<THREE.Group>(null!);
  const targetPosition = useRef(new Vector3(0, 3.5, 0));
  const [encapsulationLayers, setEncapsulationLayers] = useState(0);
  const animationState = useRef({ phase: 'idle', step: 0, timer: 0 });

  const encapsulationColors = ["#ff00ff", "#00ffff", "#ffff00", "#ff0000", "#00ff00", "#0000ff", "#ffffff"];

  useEffect(() => {
    if (isAnimating) {
      animationState.current = { phase: 'sending', step: 0, timer: 0 };
      setEncapsulationLayers(0);
      if (groupRef.current) {
        groupRef.current.position.set(0, 3.5, 0);
      }
    }
  }, [isAnimating]);

  useFrame((state, delta) => {
    if (!isAnimating || animationState.current.phase === 'idle' || !groupRef.current) return;

    const stepDuration = 1.5;
    animationState.current.timer += delta;

    if (animationState.current.timer > stepDuration) {
      animationState.current.timer = 0;
      if (animationState.current.phase === 'sending') {
        if (animationState.current.step < osiLayersData.length) {
          if (animationState.current.step > 0) {
            setEncapsulationLayers(prev => Math.min(prev + 1, 7));
          }
          animationState.current.step++;
        } else {
          animationState.current.phase = 'arrived';
        }
      }
    }
    
    if (animationState.current.phase === 'sending' && animationState.current.step < osiLayersData.length) {
      const targetY = osiLayersData[animationState.current.step].y - 0.5;
      targetPosition.current.y = targetY;
    } else if (animationState.current.phase === 'arrived') {
        animationState.current.phase = 'idle';
        onAnimationComplete();
    }

    groupRef.current.position.lerp(targetPosition.current, 0.05);
  });

  return (
    <group ref={groupRef}>
      <Box args={[0.5, 0.5, 0.5]} material-color="white" />
      {Array.from({ length: encapsulationLayers }).map((_, i) => (
        <EncapsulationLayer
          key={i}
          targetScale={1 + (i + 1) * 0.4}
          color={encapsulationColors[i % encapsulationColors.length]}
        />
      ))}
    </group>
  );
};

const EncapsulationLayer = ({ targetScale, color }: { targetScale: number, color: string }) => {
  const ref = useRef<THREE.Mesh>(null!);

  useFrame(() => {
    if (ref.current) {
      ref.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1);
    }
  });

  return (
    <Box ref={ref} args={[0.5, 0.5, 0.5]} scale={[0.01, 0.01, 0.01]}>
      <meshStandardMaterial
        color={color}
        transparent
        opacity={0.2}
        metalness={0.5}
        roughness={0.3}
      />
    </Box>
  );
};


const OsiLayer3D = ({ position, name }: { position: [number, number, number], name: string }) => {
  return (
    <group position={position}>
      <mesh>
        <boxGeometry args={[6, 0.1, 4]} />
        <meshStandardMaterial
          color="#0e7490"
          transparent
          opacity={0.3}
          metalness={0.8}
          roughness={0.2}
          emissive="#0891b2"
          emissiveIntensity={0.5}
        />
      </mesh>
      <Text position={[0, 0.2, 0]} fontSize={0.3} color="white" anchorX="center">
        {name}
      </Text>
    </group>
  );
};

export const OsiVisualization = ({ isAnimating, onAnimationComplete }: OsiVisualizationProps) => {
  return (
    <div className="w-full h-full min-h-[400px] rounded-lg bg-black relative">
       <div className="absolute top-2 left-2 text-xs text-gray-400 z-10">
        使用鼠标滚轮缩放，拖动旋转
      </div>
      <Canvas camera={{ position: [0, 1, 12], fov: 50 }}>
        <ambientLight intensity={0.8} />
        <pointLight position={[10, 10, 10]} color="cyan" intensity={2} />
        <pointLight position={[-10, -10, -10]} color="magenta" intensity={2} />

        <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade speed={1} />
        
        {osiLayersData.map((layer) => (
          <OsiLayer3D key={layer.y} position={[0, layer.y, 0]} name={layer.name} />
        ))}

        <Packet isAnimating={isAnimating} onAnimationComplete={onAnimationComplete} />

        <OrbitControls enablePan={false} minDistance={5} maxDistance={20} />
      </Canvas>
    </div>
  );
};