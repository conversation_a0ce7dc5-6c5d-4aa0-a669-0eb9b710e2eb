[{"layer": 7, "name": "应用层", "englishName": "Application Layer", "pdu": "数据 (Data)", "description": "OSI模型中最靠近用户的一层，为应用程序提供网络服务，负责处理应用程序的通信需求，例如文件传输、电子邮件、网页浏览等。", "details": ["HTTP (超文本传输协议)", "FTP (文件传输协议)", "SMTP (简单邮件传输协议)", "DNS (域名系统)", "Telnet (远程登录协议)"]}, {"layer": 6, "name": "表示层", "englishName": "Presentation Layer", "pdu": "数据 (Data)", "description": "确保一个系统的应用层所发送的信息可以被另一个系统的应用层读取。它负责数据的转换、加密和压缩，处理数据格式的差异，使其对应用程序透明。", "details": ["数据格式化 (如 ASCII, EBCDIC)", "数据加密与解密 (如 SSL/TLS)", "数据压缩", "JPEG, GIF, PNG, MPEG"]}, {"layer": 5, "name": "会话层", "englishName": "Session Layer", "pdu": "数据 (Data)", "description": "负责建立、管理和终止两个通信主机之间的会话连接。它管理对话控制，决定通信模式（单工、半双工、全双工），并使用同步点在数据流中插入校验点，以实现数据同步和恢复。", "details": ["会话建立、管理、终止", "对话控制 (Dialog Control)", "同步 (Synchronization)", "NetBIOS, RPC (远程过程调用)"]}, {"layer": 4, "name": "传输层", "englishName": "Transport Layer", "pdu": "段 (Segment) / 数据报 (Datagram)", "description": "提供端到端的通信服务，负责在源主机和目标主机之间建立、维护和终止连接。它处理数据的分段、传输、重组，并提供流量控制和差错校验，确保数据的可靠传输。", "details": ["TCP (传输控制协议) - 面向连接", "UDP (用户数据报协议) - 无连接", "端口号寻址", "流量控制", "差错控制"]}, {"layer": 3, "name": "网络层", "englishName": "Network Layer", "pdu": "包 (Packet)", "description": "负责将数据包从源主机路由到目标主机，即使它们位于不同的网络中。它处理逻辑地址（如IP地址）寻址、路由选择和流量控制，决定数据在网络中的最佳路径。", "details": ["IP (网际协议)", "ICMP (互联网控制消息协议)", "ARP (地址解析协议)", "路由器 (Router)", "逻辑寻址与路由"]}, {"layer": 2, "name": "数据链路层", "englishName": "Data Link Layer", "pdu": "帧 (Frame)", "description": "在物理层提供的比特流服务的基础上，建立相邻节点之间的数据链路。它负责将网络层的数据包封装成帧，进行物理地址（MAC地址）寻址、差错检测和流量控制。", "details": ["以太网 (Ethernet)", "PPP (点对点协议)", "MAC (媒体访问控制) 地址", "交换机 (Switch)", "网桥 (Bridge)"]}, {"layer": 1, "name": "物理层", "englishName": "Physical Layer", "pdu": "比特 (Bit)", "description": "OSI模型的最底层，负责在物理媒介上传输原始的比特流。它定义了接口和传输介质的物理特性，如电压、针脚、线缆、光纤等，以建立、维护和断开物理连接。", "details": ["比特流传输", "定义物理规格 (电压, 接口)", "集线器 (<PERSON><PERSON>)", "中继器 (Repeater)", "网线, 光纤, 无线电波"]}]